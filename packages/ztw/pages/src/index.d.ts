// Type definitions for @ztw/pages library
// Auto-generated during build process
import React from 'react';

declare module '@ztw/pages' {
  // Main library export
  export interface EdgeUXLibrary {
    [key: string]: any;
  }

  // Component exports (memory-safe library)
  // AppLayout commented out - causes memory issues
  // export const AppLayout: React.ComponentType<any>;
  // AppProvider commented out - causes memory issues
  export const AppProvider: React.ComponentType<any>;
  export const Gateways: React.ComponentType<any>;

  // Additional utility types
  export interface StoreConfig {
    [key: string]: any;
  }

  const library: EdgeUXLibrary;
  export default library;
}

// UMD global export
declare global {
  interface Window {
    EdgeUXLibrary: any;
  }
}

export = EdgeUXLibrary;
declare const EdgeUXLibrary: any;
